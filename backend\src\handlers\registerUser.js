const { v4: uuidv4 } = require('uuid')
const serviceFactory = require('../services/ServiceFactory')
const { registerUserSchema, validateRequest } = require('../utils/validation')
const { successResponse, errorResponse, validationErrorResponse } = require('../utils/response')

/**
 * Lambda handler for user registration
 * POST /users/register
 */
exports.handler = async (event) => {
  console.log('Register user request:', JSON.stringify(event, null, 2))

  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Requested-With',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
        'Access-Control-Allow-Credentials': 'false',
        'Access-Control-Max-Age': '86400'
      },
      body: ''
    }
  }

  try {
    // Parse request body
    let requestBody
    try {
      requestBody = JSON.parse(event.body || '{}')
    } catch (error) {
      return errorResponse('Invalid JSON in request body', 400, 'INVALID_JSON')
    }

    // Validate request data
    const validation = validateRequest(requestBody, registerUserSchema)
    if (!validation.isValid) {
      return validationErrorResponse(validation.errors)
    }

    const { username, email, password } = validation.value

    // Get services from factory
    const dynamoService = serviceFactory.getDynamoService()
    const cognitoService = serviceFactory.getCognitoService()

    // Check if user already exists in DynamoDB
    const existingUser = await dynamoService.getUserByEmail(email)
    if (existingUser) {
      return errorResponse('User with this email already exists', 409, 'USER_EXISTS')
    }

    // Generate unique user ID
    const userId = uuidv4()

    // Create user in Cognito
    let cognitoUser
    try {
      cognitoUser = await cognitoService.createUser(username, email, password)
    } catch (error) {
      console.error('Cognito user creation failed:', error)

      if (error.message.includes('already exists')) {
        return errorResponse('User with this email already exists', 409, 'USER_EXISTS')
      }

      return errorResponse('Failed to create user account', 500, 'COGNITO_ERROR')
    }

    // Store user data in DynamoDB
    try {
      const userData = {
        userId: userId,
        username: username,
        email: email
      }

      const createdUser = await dynamoService.createUser(userData)

      // Return success response (excluding sensitive data)
      const responseData = {
        userId: createdUser.userId,
        username: createdUser.username,
        email: createdUser.email,
        createdAt: createdUser.createdAt
      }

      return successResponse(responseData, 201)
    } catch (error) {
      console.error('DynamoDB user creation failed:', error)
      
      // If DynamoDB fails, we should ideally clean up the Cognito user
      // For now, we'll log the error and return a server error
      console.error('User created in Cognito but failed to store in DynamoDB. Manual cleanup may be required.')
      
      return errorResponse('Failed to complete user registration', 500, 'DATABASE_ERROR')
    }

  } catch (error) {
    console.error('Unexpected error in registerUser:', error)
    return errorResponse('Internal server error', 500, 'INTERNAL_ERROR')
  }
}
