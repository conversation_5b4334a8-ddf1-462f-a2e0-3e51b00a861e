// Global test setup for Jest
// This file sets up common mocks and environment variables for all tests

// Set up environment variables for all tests
process.env.USERS_TABLE_NAME = 'test-users-table'
process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
process.env.LIKES_TABLE_NAME = 'test-likes-table'
process.env.COMMENTS_TABLE_NAME = 'test-comments-table'
process.env.FOLLOWS_TABLE_NAME = 'test-follows-table'
process.env.REPORTS_TABLE_NAME = 'test-reports-table'
process.env.SUBSCRIPTIONS_TABLE_NAME = 'test-subscriptions-table'
process.env.TRANSACTIONS_TABLE_NAME = 'test-transactions-table'
process.env.USER_POOL_ID = 'test-user-pool'
process.env.USER_POOL_CLIENT_ID = 'test-client-id'
process.env.AWS_REGION = 'us-east-1'
process.env.S3_BUCKET_NAME = 'test-bucket'
process.env.STRIPE_SECRET_KEY = 'sk_test_123'
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123'

// Mock AWS SDK modules globally
jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  PutItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn(),
  ScanCommand: jest.fn(),
  QueryCommand: jest.fn()
}))

jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn().mockImplementation(() => ({
      send: jest.fn()
    }))
  },
  PutCommand: jest.fn(),
  GetCommand: jest.fn(),
  UpdateCommand: jest.fn(),
  DeleteCommand: jest.fn(),
  ScanCommand: jest.fn(),
  QueryCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  marshall: jest.fn().mockImplementation((obj) => obj),
  unmarshall: jest.fn().mockImplementation((obj) => obj)
}))

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  GetObjectCommand: jest.fn(),
  PutObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn()
}))

jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn()
}))

jest.mock('@aws-sdk/client-cognito-identity-provider', () => ({
  CognitoIdentityProviderClient: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  AdminCreateUserCommand: jest.fn(),
  AdminSetUserPasswordCommand: jest.fn(),
  AdminConfirmSignUpCommand: jest.fn(),
  AdminDeleteUserCommand: jest.fn()
}))

// Note: Stripe mocking will be handled in individual test files that need it

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-123')
}))

// Suppress console logs during tests unless explicitly needed
const originalConsoleLog = console.log
const originalConsoleError = console.error

beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks()

  // Set test environment for ServiceFactory
  process.env.NODE_ENV = 'test'

  // Reset ServiceFactory
  const serviceFactory = require('../services/ServiceFactory')
  serviceFactory.reset()
  serviceFactory.setTestMode(true)

  // Suppress console output unless VERBOSE_TESTS is set
  if (!process.env.VERBOSE_TESTS) {
    console.log = jest.fn()
    console.error = jest.fn()
  }
})

afterEach(() => {
  // Restore console if it was mocked
  if (!process.env.VERBOSE_TESTS) {
    console.log = originalConsoleLog
    console.error = originalConsoleError
  }
})

// Global test utilities
global.createMockEvent = (body, pathParameters = {}, userId = 'test-user-123') => ({
  body: JSON.stringify(body),
  pathParameters,
  requestContext: {
    authorizer: {
      claims: {
        sub: userId
      }
    }
  }
})

global.createUnauthenticatedEvent = (body, pathParameters = {}) => ({
  body: JSON.stringify(body),
  pathParameters,
  requestContext: {}
})
