const { handler } = require('../handlers/registerUser')
const serviceFactory = require('../services/ServiceFactory')

describe('registerUser Lambda Handler', () => {
  let mockDynamoService
  let mockCognitoService

  beforeEach(() => {
    // Get fresh mock services from factory after reset
    mockDynamoService = serviceFactory.getDynamoService()
    mockCognitoService = serviceFactory.getCognitoService()
  })

  const createEvent = (body) => ({
    body: JSON.stringify(body),
    requestContext: {},
    headers: {}
  })

  describe('Successful registration', () => {
    it('should register a new user successfully', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockCognitoUser = {
        userId: 'cognito-user-id',
        email: '<EMAIL>',
        username: 'testuser'
      }

      const mockDynamoUser = {
        userId: 'test-uuid-1',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isActive: true
      }

      // Setup mocks - getUserByEmail should return null (user doesn't exist)
      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockResolvedValue(mockCognitoUser)
      mockDynamoService.createUser.mockResolvedValue(mockDynamoUser)



      const event = createEvent(requestBody)
      const result = await handler(event)



      expect(result.statusCode).toBe(201)

      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(true)
      expect(responseBody.data).toMatchObject({
        userId: mockDynamoUser.userId,
        username: mockDynamoUser.username,
        email: mockDynamoUser.email,
        createdAt: mockDynamoUser.createdAt
      })

      // Verify service calls
      expect(mockDynamoService.getUserByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(mockCognitoService.createUser).toHaveBeenCalledWith('testuser', '<EMAIL>', 'TestPass123')
      expect(mockDynamoService.createUser).toHaveBeenCalledWith({
        userId: 'test-uuid-1',
        username: 'testuser',
        email: '<EMAIL>'
      })
    })
  })

  describe('Validation errors', () => {
    it('should return validation error for invalid email', async () => {
      const requestBody = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'TestPass123'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for weak password', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'weak'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for invalid username', async () => {
      const requestBody = {
        username: 'ab', // Too short
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('User already exists', () => {
    it('should return error when user already exists in DynamoDB', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const existingUser = {
        userId: 'existing-user-id',
        email: '<EMAIL>',
        username: 'existinguser'
      }

      // Mock getUserByEmail to return existing user
      mockDynamoService.getUserByEmail.mockResolvedValue(existingUser)

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(409)

      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('USER_EXISTS')
      expect(responseBody.error.message).toBe('User with this email already exists')

      // Verify that Cognito createUser was not called
      expect(mockCognitoService.createUser).not.toHaveBeenCalled()
      expect(mockDynamoService.createUser).not.toHaveBeenCalled()
    })
  })

  describe('Service errors', () => {
    it('should handle Cognito service errors', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      // Setup mocks - user doesn't exist, but Cognito fails
      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockRejectedValue(new Error('Cognito service error'))

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(500)

      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('COGNITO_ERROR')
      expect(responseBody.error.message).toBe('Failed to create user account')

      // Verify that DynamoDB createUser was not called
      expect(mockDynamoService.createUser).not.toHaveBeenCalled()
    })

    it('should handle DynamoDB service errors', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockCognitoUser = {
        userId: 'cognito-user-id',
        email: '<EMAIL>',
        username: 'testuser'
      }

      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockResolvedValue(mockCognitoUser)
      mockDynamoService.createUser.mockRejectedValue(new Error('DynamoDB error'))

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(500)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('DATABASE_ERROR')
    })
  })

  describe('Invalid request format', () => {
    it('should handle invalid JSON in request body', async () => {
      const event = {
        body: 'invalid json',
        requestContext: {},
        headers: {}
      }

      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('INVALID_JSON')
    })

    it('should handle missing request body', async () => {
      const event = {
        requestContext: {},
        headers: {}
      }

      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })
  })
})
