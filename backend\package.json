{"name": "tunami-backend", "version": "1.0.0", "description": "Tunami MVP Backend - User Service", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "sam build", "deploy:dev": "sam deploy --parameter-overrides Environment=dev", "deploy:staging": "sam deploy --parameter-overrides Environment=staging", "local": "sam local start-api"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "@aws-sdk/s3-request-presigner": "^3.450.0", "@aws-sdk/util-dynamodb": "^3.450.0", "uuid": "^9.0.1", "joi": "^17.11.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.126", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "eslint": "^8.53.0", "eslint-config-standard": "^17.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.js"], "collectCoverageFrom": ["src/**/*.{js,ts}", "!src/**/*.d.ts", "!src/__tests__/**"], "coverageReporters": ["text", "lcov", "html"], "coverageDirectory": "coverage", "testMatch": ["**/__tests__/**/*.test.{js,ts}", "**/?(*.)+(spec|test).{js,ts}"], "moduleFileExtensions": ["js", "ts"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "keywords": ["aws", "lambda", "serverless", "cognito", "dynamodb"], "author": "Tunami Team", "license": "MIT"}