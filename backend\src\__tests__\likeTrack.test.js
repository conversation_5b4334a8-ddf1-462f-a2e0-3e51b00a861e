// Mock AWS SDK before importing handler
jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  PutItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  marshall: jest.fn(),
  unmarshall: jest.fn()
}))

const { handler } = require('../handlers/likeTrack')
const { DynamoDBClient, PutItemCommand, DeleteItemCommand, GetItemCommand, UpdateItemCommand } = require('@aws-sdk/client-dynamodb')
const { marshall, unmarshall } = require('@aws-sdk/util-dynamodb')

describe('likeTrack Lambda Handler', () => {
  let mockDynamoSend
  let mockDynamoClient

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.LIKES_TABLE_NAME = 'test-likes-table'
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AWS_REGION = 'us-east-1'

    // Mock DynamoDB client
    mockDynamoSend = jest.fn()
    mockDynamoClient = {
      send: mockDynamoSend
    }
    DynamoDBClient.mockImplementation(() => mockDynamoClient)

    // Mock marshall/unmarshall functions
    marshall.mockImplementation((obj) => obj)
    unmarshall.mockImplementation((obj) => obj)
  })

  const createMockEvent = (trackId = 'test-track-123', userId = 'test-user-123') => ({
    pathParameters: {
      trackId: trackId
    },
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should like a track successfully', async () => {
      // Mock: like doesn't exist, track exists
      mockDynamoSend
        .mockResolvedValueOnce({ Item: undefined }) // Check existing like
        .mockResolvedValueOnce({ Item: { trackId: 'test-track-123' } }) // Check track exists
        .mockResolvedValueOnce({}) // Create like
        .mockResolvedValueOnce({}) // Update track like count

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.action).toBe('liked')

      // Verify all DynamoDB operations were called
      expect(mockDynamoSend).toHaveBeenCalledTimes(4)
    })

    test('should unlike a track successfully', async () => {
      // Mock: like exists, track exists
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { userId: 'test-user-123', trackId: 'test-track-123' } }) // Check existing like
        .mockResolvedValueOnce({ Item: { trackId: 'test-track-123' } }) // Check track exists
        .mockResolvedValueOnce({}) // Delete like
        .mockResolvedValueOnce({}) // Update track like count

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.action).toBe('unliked')

      // Verify all DynamoDB operations were called
      expect(mockDynamoSend).toHaveBeenCalledTimes(4)
    })

    test('should handle track not found', async () => {
      // Mock: like doesn't exist, track doesn't exist
      mockDynamoSend
        .mockResolvedValueOnce({ Item: undefined }) // Check existing like
        .mockResolvedValueOnce({ Item: undefined }) // Check track exists

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing trackId', async () => {
      const event = {
        pathParameters: {},
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        }
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      mockDynamoSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DATABASE_ERROR')
    })
  })
})
