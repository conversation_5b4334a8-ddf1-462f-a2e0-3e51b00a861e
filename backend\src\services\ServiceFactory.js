/**
 * Service Factory for dependency injection
 * Provides real services in production and mock services in test environment
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb')
const { S3Client } = require('@aws-sdk/client-s3')
const { CognitoIdentityProviderClient } = require('@aws-sdk/client-cognito-identity-provider')

class ServiceFactory {
  constructor() {
    this.isTestEnvironment = process.env.NODE_ENV === 'test' || process.env.JEST_WORKER_ID !== undefined
    this._dynamoClient = null
    this._s3Client = null
    this._cognitoClient = null
    this._dynamoService = null
    this._cognitoService = null
  }

  /**
   * Get DynamoDB client (real or mocked)
   */
  getDynamoClient() {
    if (this.isTestEnvironment) {
      // Return cached mock client for tests
      if (!this._mockDynamoClient) {
        this._mockDynamoClient = {
          send: jest.fn()
        }
      }
      return this._mockDynamoClient
    }

    if (!this._dynamoClient) {
      this._dynamoClient = new DynamoDBClient({ 
        region: process.env.AWS_REGION || 'us-east-1' 
      })
    }
    return this._dynamoClient
  }

  /**
   * Get S3 client (real or mocked)
   */
  getS3Client() {
    if (this.isTestEnvironment) {
      // Return mock client for tests
      return {
        send: jest.fn()
      }
    }

    if (!this._s3Client) {
      this._s3Client = new S3Client({ 
        region: process.env.AWS_REGION || 'us-east-1' 
      })
    }
    return this._s3Client
  }

  /**
   * Get Cognito client (real or mocked)
   */
  getCognitoClient() {
    if (this.isTestEnvironment) {
      // Return mock client for tests
      return {
        send: jest.fn()
      }
    }

    if (!this._cognitoClient) {
      this._cognitoClient = new CognitoIdentityProviderClient({ 
        region: process.env.AWS_REGION || 'us-east-1' 
      })
    }
    return this._cognitoClient
  }

  /**
   * Get DynamoDB service (real or mocked)
   */
  getDynamoService() {
    if (this.isTestEnvironment) {
      // Return cached mock service for tests
      if (!this._mockDynamoService) {
        this._mockDynamoService = {
          getUserById: jest.fn(),
          getUserByEmail: jest.fn(),
          createUser: jest.fn(),
          updateUser: jest.fn(),
          deleteUser: jest.fn(),
          getTrackById: jest.fn(),
          createTrack: jest.fn(),
          updateTrack: jest.fn(),
          deleteTrack: jest.fn(),
          listTracks: jest.fn(),
          searchTracks: jest.fn(),
          createLike: jest.fn(),
          deleteLike: jest.fn(),
          getLike: jest.fn(),
          createComment: jest.fn(),
          getComments: jest.fn(),
          createFollow: jest.fn(),
          deleteFollow: jest.fn(),
          getFollowers: jest.fn(),
          getFollowing: jest.fn(),
          createReport: jest.fn(),
          getReports: jest.fn(),
          updateReport: jest.fn(),
          createSubscription: jest.fn(),
          updateSubscription: jest.fn(),
          getSubscription: jest.fn(),
          createTransaction: jest.fn(),
          getTransactions: jest.fn()
        }
      }
      return this._mockDynamoService
    }

    if (!this._dynamoService) {
      const DynamoService = require('./dynamoService')
      this._dynamoService = new DynamoService()
    }
    return this._dynamoService
  }

  /**
   * Get Cognito service (real or mocked)
   */
  getCognitoService() {
    if (this.isTestEnvironment) {
      // Return cached mock service for tests
      if (!this._mockCognitoService) {
        this._mockCognitoService = {
          createUser: jest.fn(),
          setUserPassword: jest.fn(),
          confirmUser: jest.fn(),
          deleteUser: jest.fn(),
          adminCreateUser: jest.fn(),
          adminSetUserPassword: jest.fn(),
          adminConfirmSignUp: jest.fn(),
          adminDeleteUser: jest.fn()
        }
      }
      return this._mockCognitoService
    }

    if (!this._cognitoService) {
      const CognitoService = require('./cognitoService')
      this._cognitoService = new CognitoService()
    }
    return this._cognitoService
  }

  /**
   * Reset all services (useful for testing)
   */
  reset() {
    this._dynamoClient = null
    this._s3Client = null
    this._cognitoClient = null
    this._dynamoService = null
    this._cognitoService = null
    this._mockDynamoClient = null
    this._mockDynamoService = null
    this._mockCognitoService = null
  }

  /**
   * Set test mode explicitly
   */
  setTestMode(isTest = true) {
    this.isTestEnvironment = isTest
  }
}

// Export singleton instance
module.exports = new ServiceFactory()
