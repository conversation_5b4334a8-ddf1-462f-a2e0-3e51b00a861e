const { handler } = require('../handlers/addComment')
const serviceFactory = require('../services/ServiceFactory')

describe('addComment Lambda Handler', () => {
  let mockDynamoClient

  beforeEach(() => {
    // Get fresh mock client from factory after reset
    mockDynamoClient = serviceFactory.getDynamoClient()
  })

  const createMockEvent = (body, trackId = 'test-track-123', userId = 'test-user-123') => ({
    body: JSON.stringify(body),
    pathParameters: {
      trackId: trackId
    },
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should add comment successfully', async () => {
      const requestBody = {
        content: 'Great track!'
      }

      // Mock: track exists
      mockDynamoClient.send
        .mockResolvedValueOnce({ Item: { trackId: 'test-track-123' } }) // Check track exists
        .mockResolvedValueOnce({}) // Create comment
        .mockResolvedValueOnce({}) // Update track comment count

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('commentId')
      expect(response.data).toHaveProperty('trackId', 'test-track-123')
      expect(response.data).toHaveProperty('userId', 'test-user-123')
      expect(response.data).toHaveProperty('content', 'Great track!')

      // Verify all DynamoDB operations were called
      expect(mockDynamoClient.send).toHaveBeenCalledTimes(3)
    })

    test('should handle track not found', async () => {
      const requestBody = {
        content: 'Great track!'
      }

      // Mock: track doesn't exist
      mockDynamoClient.send.mockResolvedValueOnce({ Item: undefined })

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing comment text', async () => {
      const requestBody = {}

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject empty comment text', async () => {
      const requestBody = {
        content: ''
      }

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject comment text that is too long', async () => {
      const requestBody = {
        content: 'A'.repeat(501) // 501 characters (max is 500)
      }

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing trackId', async () => {
      const requestBody = {
        content: 'Great track!'
      }

      const event = {
        body: JSON.stringify(requestBody),
        pathParameters: {},
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        }
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const requestBody = {
        content: 'Great track!'
      }

      const event = {
        body: JSON.stringify(requestBody),
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      const requestBody = {
        content: 'Great track!'
      }

      // Mock: track exists, but comment creation fails
      mockDynamoClient.send
        .mockResolvedValueOnce({ Item: { trackId: 'test-track-123' } }) // Track exists
        .mockRejectedValue(new Error('DynamoDB error')) // Comment creation fails

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('COMMENT_ERROR')
    })
  })
})
